import 'dart:async';

import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_match.dart' as base_model;
import 'package:dauntless/repositories/websocket_repository.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockWebSocketRepository extends Mock implements WebSocketRepository {}

void main() {
  group('ServerNotificationsUseCase', () {
    late ServerNotificationsUseCase useCase;
    late MockWebSocketRepository mockWebSocketRepository;
    late StreamController<GameMatch> matchUpdatesController;
    late StreamController<bool> connectionStatusController;
    late StreamController<dynamic> topicStreamController;

    setUp(() {
      mockWebSocketRepository = MockWebSocketRepository();
      useCase = ServerNotificationsUseCase(mockWebSocketRepository);
      
      // Set up stream controllers for mocking
      matchUpdatesController = StreamController<GameMatch>.broadcast();
      connectionStatusController = StreamController<bool>.broadcast();
      topicStreamController = StreamController<dynamic>.broadcast();
      
      // Set up default mock behaviors
      when(() => mockWebSocketRepository.matchUpdates)
          .thenAnswer((_) => matchUpdatesController.stream);
      when(() => mockWebSocketRepository.connectionStatusUpdates)
          .thenAnswer((_) => connectionStatusController.stream);
      when(() => mockWebSocketRepository.getTopicStream(any()))
          .thenAnswer((_) => topicStreamController.stream);
      when(() => mockWebSocketRepository.isConnected).thenReturn(false);
      when(() => mockWebSocketRepository.hasTopicListeners(any())).thenReturn(true);
      when(() => mockWebSocketRepository.getTopicDebugInfo()).thenReturn({'open_matches': true});
    });

    tearDown(() {
      matchUpdatesController.close();
      connectionStatusController.close();
      topicStreamController.close();
    });

    group('Constructor and Initialization', () {
      test('should initialize with WebSocketRepository dependency', () {
        expect(useCase, isA<ServerNotificationsUseCase>());
      });

      test('should initialize with empty match factories map', () {
        // The match factories map is private, but we can verify behavior
        expect(useCase, isNotNull);
      });

      test('should be instantiable multiple times', () {
        final useCase1 = ServerNotificationsUseCase(mockWebSocketRepository);
        final useCase2 = ServerNotificationsUseCase(mockWebSocketRepository);
        
        expect(useCase1, isNot(same(useCase2)));
        expect(useCase1, isA<ServerNotificationsUseCase>());
        expect(useCase2, isA<ServerNotificationsUseCase>());
      });
    });

    group('Stream Properties', () {
      test('should provide match updates stream from repository', () {
        final stream = useCase.matchUpdates;
        expect(stream, isA<Stream<GameMatch>>());
        verify(() => mockWebSocketRepository.matchUpdates).called(1);
      });

      test('should provide connection status updates stream from repository', () {
        final stream = useCase.connectionStatusUpdates;
        expect(stream, isA<Stream<bool>>());
        verify(() => mockWebSocketRepository.connectionStatusUpdates).called(1);
      });

      test('should provide open matches updates stream with transformation', () {
        final stream = useCase.openMatchesUpdates;
        expect(stream, isA<Stream<List<base_model.GameMatch>>>());
        verify(() => mockWebSocketRepository.getTopicStream('open_matches')).called(1);
      });

      test('should handle open matches stream data transformation', () async {
        // Set up mock data in the correct format that would come from the WebSocket
        final now = DateTime.now().millisecondsSinceEpoch;
        final testData = {
          'data': {
            'matches': [
              {
                'id': 'match1',
                'gameTypeId': 'test-game',
                'creatorId': 'test-creator',
                'status': 'open',
                'created_at': now,
                'updated_at': now,
                'playerSlots': [],
              }
            ]
          }
        };

        // Create a new controller for this specific test
        final testController = StreamController<dynamic>.broadcast();
        when(() => mockWebSocketRepository.getTopicStream('open_matches'))
            .thenAnswer((_) => testController.stream);

        final stream = useCase.openMatchesUpdates;
        final streamFuture = stream.first;

        // Add test data to the stream
        testController.add(testData);

        // Verify the transformation works
        final result = await streamFuture;
        expect(result, isA<List<base_model.GameMatch>>());
        expect(result.length, equals(1));
        expect(result.first.id, equals('match1'));

        testController.close();
      });
    });

    group('Connection Status', () {
      test('should return connection status from repository', () {
        when(() => mockWebSocketRepository.isConnected).thenReturn(true);
        
        expect(useCase.isConnected, isTrue);
        verify(() => mockWebSocketRepository.isConnected).called(1);
      });

      test('should return false when repository is disconnected', () {
        when(() => mockWebSocketRepository.isConnected).thenReturn(false);
        
        expect(useCase.isConnected, isFalse);
        verify(() => mockWebSocketRepository.isConnected).called(1);
      });
    });

    group('Topic Management', () {
      test('should get topic stream from repository', () {
        const testTopic = 'test_topic';
        
        final stream = useCase.getTopicStream(testTopic);
        expect(stream, isA<Stream<dynamic>>());
        verify(() => mockWebSocketRepository.getTopicStream(testTopic)).called(1);
      });

      test('should subscribe to topic through repository', () async {
        const testTopic = 'test_topic';
        when(() => mockWebSocketRepository.subscribeToTopic(testTopic))
            .thenAnswer((_) async {});
        
        await useCase.subscribeToTopic(testTopic);
        verify(() => mockWebSocketRepository.subscribeToTopic(testTopic)).called(1);
      });

      test('should unsubscribe from topic through repository', () {
        const testTopic = 'test_topic';
        when(() => mockWebSocketRepository.unsubscribeFromTopic(testTopic))
            .thenReturn(null);
        
        useCase.unsubscribeFromTopic(testTopic);
        verify(() => mockWebSocketRepository.unsubscribeFromTopic(testTopic)).called(1);
      });

      test('should handle subscription errors gracefully', () async {
        const testTopic = 'test_topic';
        when(() => mockWebSocketRepository.subscribeToTopic(testTopic))
            .thenThrow(Exception('Subscription failed'));
        
        expect(() => useCase.subscribeToTopic(testTopic), throwsException);
        verify(() => mockWebSocketRepository.subscribeToTopic(testTopic)).called(1);
      });
    });

    group('Message Handling', () {
      test('should send message through repository', () {
        final testMessage = {'type': 'test', 'data': 'test_data'};
        when(() => mockWebSocketRepository.sendMessage(testMessage))
            .thenReturn(null);
        
        useCase.sendMessage(testMessage);
        verify(() => mockWebSocketRepository.sendMessage(testMessage)).called(1);
      });

      test('should handle empty message', () {
        final emptyMessage = <String, dynamic>{};
        when(() => mockWebSocketRepository.sendMessage(emptyMessage))
            .thenReturn(null);
        
        useCase.sendMessage(emptyMessage);
        verify(() => mockWebSocketRepository.sendMessage(emptyMessage)).called(1);
      });

      test('should handle complex message structure', () {
        final complexMessage = {
          'type': 'complex',
          'data': {
            'nested': {'value': 123},
            'array': [1, 2, 3],
            'boolean': true,
          }
        };
        when(() => mockWebSocketRepository.sendMessage(complexMessage))
            .thenReturn(null);
        
        useCase.sendMessage(complexMessage);
        verify(() => mockWebSocketRepository.sendMessage(complexMessage)).called(1);
      });
    });

    group('Connection Management', () {
      test('should connect through repository', () async {
        when(() => mockWebSocketRepository.connect())
            .thenAnswer((_) async {});
        
        await useCase.connect();
        verify(() => mockWebSocketRepository.connect()).called(1);
      });

      test('should disconnect through repository', () {
        when(() => mockWebSocketRepository.disconnect())
            .thenReturn(null);
        
        useCase.disconnect();
        verify(() => mockWebSocketRepository.disconnect()).called(1);
      });

      test('should handle connection errors and rethrow', () async {
        when(() => mockWebSocketRepository.connect())
            .thenThrow(Exception('Connection failed'));
        
        expect(() => useCase.connect(), throwsException);
        verify(() => mockWebSocketRepository.connect()).called(1);
      });

      test('should handle connection timeout', () async {
        when(() => mockWebSocketRepository.connect())
            .thenThrow(TimeoutException('Connection timeout', Duration(seconds: 5)));
        
        expect(() => useCase.connect(), throwsA(isA<TimeoutException>()));
        verify(() => mockWebSocketRepository.connect()).called(1);
      });
    });

    group('Open Matches Management', () {
      test('should subscribe to open matches', () async {
        when(() => mockWebSocketRepository.subscribeToTopic('open_matches'))
            .thenAnswer((_) async {});
        
        await useCase.subscribeToOpenMatches();
        verify(() => mockWebSocketRepository.subscribeToTopic('open_matches')).called(1);
      });

      test('should unsubscribe from open matches', () {
        when(() => mockWebSocketRepository.unsubscribeFromTopic('open_matches'))
            .thenReturn(null);
        
        useCase.unsubscribeFromOpenMatches();
        verify(() => mockWebSocketRepository.unsubscribeFromTopic('open_matches')).called(1);
      });

      test('should handle open matches subscription error', () async {
        when(() => mockWebSocketRepository.subscribeToTopic('open_matches'))
            .thenThrow(Exception('Open matches subscription failed'));
        
        expect(() => useCase.subscribeToOpenMatches(), throwsException);
        verify(() => mockWebSocketRepository.subscribeToTopic('open_matches')).called(1);
      });
    });

    group('Match Actions Management', () {
      test('should subscribe to match actions for specific match', () async {
        const matchId = 'test-match-123';
        const expectedTopic = 'match:test-match-123:actions';
        
        when(() => mockWebSocketRepository.subscribeToTopic(expectedTopic))
            .thenAnswer((_) async {});
        
        await useCase.subscribeToMatchActions(matchId);
        verify(() => mockWebSocketRepository.subscribeToTopic(expectedTopic)).called(1);
      });

      test('should handle match actions subscription with empty match ID', () async {
        const matchId = '';
        const expectedTopic = 'match::actions';
        
        when(() => mockWebSocketRepository.subscribeToTopic(expectedTopic))
            .thenAnswer((_) async {});
        
        await useCase.subscribeToMatchActions(matchId);
        verify(() => mockWebSocketRepository.subscribeToTopic(expectedTopic)).called(1);
      });

      test('should handle match actions subscription error', () async {
        const matchId = 'test-match-123';
        const expectedTopic = 'match:test-match-123:actions';

        when(() => mockWebSocketRepository.subscribeToTopic(expectedTopic))
            .thenThrow(Exception('Match actions subscription failed'));

        expect(() => useCase.subscribeToMatchActions(matchId), throwsException);
        verify(() => mockWebSocketRepository.subscribeToTopic(expectedTopic)).called(1);
      });

      test('should unsubscribe from match actions for specific match', () {
        const matchId = 'test-match-456';
        const expectedTopic = 'match:test-match-456:actions';

        when(() => mockWebSocketRepository.unsubscribeFromTopic(expectedTopic))
            .thenReturn(null);

        useCase.unsubscribeFromMatchActions(matchId);
        verify(() => mockWebSocketRepository.unsubscribeFromTopic(expectedTopic)).called(1);
      });

      test('should handle unsubscribe from match actions with empty match ID', () {
        const matchId = '';
        const expectedTopic = 'match::actions';

        when(() => mockWebSocketRepository.unsubscribeFromTopic(expectedTopic))
            .thenReturn(null);

        useCase.unsubscribeFromMatchActions(matchId);
        verify(() => mockWebSocketRepository.unsubscribeFromTopic(expectedTopic)).called(1);
      });
    });

    group('Match Action Sending', () {
      test('should send match action with proper message structure', () {
        const matchId = 'test-match-789';
        final action = {'action': 'move', 'position': {'x': 5, 'y': 3}};

        final expectedMessage = {
          'type': 'match_action',
          'topic': 'match:test-match-789:actions',
          'data': action,
        };

        when(() => mockWebSocketRepository.sendMessage(expectedMessage))
            .thenReturn(null);

        useCase.sendMatchAction(matchId, action);
        verify(() => mockWebSocketRepository.sendMessage(expectedMessage)).called(1);
      });

      test('should send match action with empty action data', () {
        const matchId = 'test-match-empty';
        final action = <String, dynamic>{};

        final expectedMessage = {
          'type': 'match_action',
          'topic': 'match:test-match-empty:actions',
          'data': action,
        };

        when(() => mockWebSocketRepository.sendMessage(expectedMessage))
            .thenReturn(null);

        useCase.sendMatchAction(matchId, action);
        verify(() => mockWebSocketRepository.sendMessage(expectedMessage)).called(1);
      });

      test('should send match action with complex action data', () {
        const matchId = 'test-match-complex';
        final action = {
          'action': 'attack',
          'target': {'id': 'enemy-1', 'type': 'unit'},
          'weapon': {'id': 'sword-1', 'damage': 10},
          'modifiers': ['critical', 'flanking'],
          'timestamp': DateTime.now().toIso8601String(),
        };

        final expectedMessage = {
          'type': 'match_action',
          'topic': 'match:test-match-complex:actions',
          'data': action,
        };

        when(() => mockWebSocketRepository.sendMessage(expectedMessage))
            .thenReturn(null);

        useCase.sendMatchAction(matchId, action);
        verify(() => mockWebSocketRepository.sendMessage(expectedMessage)).called(1);
      });

      test('should handle match action sending with empty match ID', () {
        const matchId = '';
        final action = {'action': 'test'};

        final expectedMessage = {
          'type': 'match_action',
          'topic': 'match::actions',
          'data': action,
        };

        when(() => mockWebSocketRepository.sendMessage(expectedMessage))
            .thenReturn(null);

        useCase.sendMatchAction(matchId, action);
        verify(() => mockWebSocketRepository.sendMessage(expectedMessage)).called(1);
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete workflow: connect, subscribe, send, disconnect', () async {
        const matchId = 'integration-match';
        final action = {'action': 'join'};

        // Set up all mocks
        when(() => mockWebSocketRepository.connect()).thenAnswer((_) async {});
        when(() => mockWebSocketRepository.subscribeToTopic('open_matches')).thenAnswer((_) async {});
        when(() => mockWebSocketRepository.subscribeToTopic('match:integration-match:actions')).thenAnswer((_) async {});
        when(() => mockWebSocketRepository.sendMessage(any())).thenReturn(null);
        when(() => mockWebSocketRepository.disconnect()).thenReturn(null);

        // Execute workflow
        await useCase.connect();
        await useCase.subscribeToOpenMatches();
        await useCase.subscribeToMatchActions(matchId);
        useCase.sendMatchAction(matchId, action);
        useCase.disconnect();

        // Verify all calls
        verify(() => mockWebSocketRepository.connect()).called(1);
        verify(() => mockWebSocketRepository.subscribeToTopic('open_matches')).called(1);
        verify(() => mockWebSocketRepository.subscribeToTopic('match:integration-match:actions')).called(1);
        verify(() => mockWebSocketRepository.sendMessage(any())).called(1);
        verify(() => mockWebSocketRepository.disconnect()).called(1);
      });

      test('should handle multiple topic subscriptions', () async {
        const topics = ['topic1', 'topic2', 'topic3'];

        for (final topic in topics) {
          when(() => mockWebSocketRepository.subscribeToTopic(topic)).thenAnswer((_) async {});
        }

        for (final topic in topics) {
          await useCase.subscribeToTopic(topic);
        }

        for (final topic in topics) {
          verify(() => mockWebSocketRepository.subscribeToTopic(topic)).called(1);
        }
      });

      test('should handle stream data flow for open matches', () async {
        // Set up a realistic open matches data flow
        final testController = StreamController<dynamic>.broadcast();
        when(() => mockWebSocketRepository.getTopicStream('open_matches'))
            .thenAnswer((_) => testController.stream);

        final stream = useCase.openMatchesUpdates;
        final receivedData = <List<base_model.GameMatch>>[];

        // Listen to the stream
        final subscription = stream.listen((data) {
          receivedData.add(data);
        });

        // Send test data in the correct format
        final testData = {
          'data': {
            'matches': [
              {
                'id': 'match1',
                'gameTypeId': 'game1',
                'status': 'open',
                'createdAt': DateTime.now().toIso8601String(),
                'playerSlots': [],
              },
              {
                'id': 'match2',
                'gameTypeId': 'game2',
                'status': 'open',
                'createdAt': DateTime.now().toIso8601String(),
                'playerSlots': [],
              }
            ]
          }
        };

        testController.add(testData);

        // Wait for data processing
        await Future.delayed(Duration(milliseconds: 10));

        expect(receivedData.length, equals(1));
        expect(receivedData.first.length, equals(2));
        expect(receivedData.first.first.id, equals('match1'));
        expect(receivedData.first.last.id, equals('match2'));

        await subscription.cancel();
        testController.close();
      });

      test('should handle error recovery in connection workflow', () async {
        var callCount = 0;

        // Set up mock to fail first, then succeed
        when(() => mockWebSocketRepository.connect()).thenAnswer((_) async {
          callCount++;
          if (callCount == 1) {
            throw Exception('First connection failed');
          }
          // Second call succeeds
        });

        // First connection attempt fails
        expect(() => useCase.connect(), throwsException);

        // Second connection attempt succeeds
        await useCase.connect();

        // Verify both calls were made
        verify(() => mockWebSocketRepository.connect()).called(2);
      });
    });
  });
}
